version: '3.8'

services:
  mysql:
    image: mysql:8.0.27
    container_name: hmall-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: hmall
      MYSQL_USER: hmall
      MYSQL_PASSWORD: 123456
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - ./mysql/conf/hm.cnf:/etc/mysql/conf.d/hm.cnf
      - ./mysql/init:/docker-entrypoint-initdb.d
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - hmall-network

volumes:
  mysql_data:

networks:
  hmall-network:
    driver: bridge
